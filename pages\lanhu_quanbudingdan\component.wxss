.page {
  background-color: rgba(245,245,249,1.000000);
  position: relative;
  width: 750rpx;
  height: 1628rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.group_1 {
  background-color: rgba(255,255,255,1.000000);
  height: 264rpx;
  width: 750rpx;
  display: flex;
  flex-direction: column;
}
.image-wrapper_1 {
  width: 656rpx;
  height: 24rpx;
  flex-direction: row;
  display: flex;
  margin: 36rpx 0 0 48rpx;
}
.image_1 {
  width: 54rpx;
  height: 22rpx;
}
.thumbnail_1 {
  width: 34rpx;
  height: 22rpx;
  margin-left: 468rpx;
}
.thumbnail_2 {
  width: 32rpx;
  height: 24rpx;
  margin-left: 10rpx;
}
.image_2 {
  width: 48rpx;
  height: 22rpx;
  margin-left: 10rpx;
}
.group_2 {
  width: 686rpx;
  height: 128rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 48rpx 0 0 30rpx;
}
.image-text_1 {
  width: 56rpx;
  height: 116rpx;
  margin-top: 12rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.thumbnail_3 {
  width: 34rpx;
  height: 34rpx;
  margin-left: 8rpx;
}
.text-group_1 {
  width: 56rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin-top: 42rpx;
}
.text_1 {
  width: 84rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 88rpx 0 0 56rpx;
}
.section_1 {
  width: 224rpx;
  height: 128rpx;
  margin-left: 56rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.900000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
  margin-left: 30rpx;
}
.text-wrapper_1 {
  width: 224rpx;
  height: 40rpx;
  margin-top: 44rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.text_3 {
  width: 84rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.text_4 {
  width: 84rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.text_5 {
  width: 154rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 88rpx 0 0 56rpx;
}
.group_3 {
  width: 46rpx;
  height: 8rpx;
  display: flex;
  flex-direction: row;
  margin: 12rpx 0 8rpx 34rpx;
}
.box_1 {
  background-color: rgba(64,128,255,1.000000);
  border-radius: 2rpx;
  width: 46rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
}
.list_1 {
  width: 690rpx;
  height: 828rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 30rpx 0 0 30rpx;
}
.list-items_1-0 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 16rpx;
  width: 690rpx;
  height: 398rpx;
  margin-bottom: 32rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-center;
}
.text-wrapper_2-0 {
  width: 630rpx;
  height: 34rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0 0 30rpx;
}
.text_6-0 {
  width: 372rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Light;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_7-0 {
  width: 84rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(64,128,255,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.image-text_2-0 {
  width: 624rpx;
  height: 194rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 28rpx 0 0 30rpx;
}
.image_3-0 {
  width: 194rpx;
  height: 194rpx;
}
.text-group_2-0 {
  width: 406rpx;
  height: 154rpx;
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text_8-0 {
  width: 378rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_9-0 {
  width: 394rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 26rpx;
}
.block_1-0 {
  width: 406rpx;
  height: 34rpx;
  margin-top: 26rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.text_10-0 {
  width: 134rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text-wrapper_3-0 {
  width: 212rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  font-size: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_11-0 {
  width: 212rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_12-0 {
  width: 212rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_13-0 {
  width: 212rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.group_4-0 {
  width: 360rpx;
  height: 62rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 20rpx 0 30rpx 300rpx;
}
.text-wrapper_4-0 {
  border-radius: 200rpx;
  height: 62rpx;
  border: 1px solid rgba(0,0,0,0.3);
  display: flex;
  flex-direction: column;
  width: 162rpx;
}
.text_14-0 {
  width: 112rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 14rpx 0 0 24rpx;
}
.text-wrapper_5-0 {
  background-color: rgba(64,128,255,1.000000);
  border-radius: 200rpx;
  height: 62rpx;
  display: flex;
  flex-direction: column;
  width: 162rpx;
}
.text_15-0 {
  width: 112rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 14rpx 0 0 24rpx;
}
.list-items_1-1 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 16rpx;
  width: 690rpx;
  height: 398rpx;
  margin-bottom: 32rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-center;
}
.text-wrapper_2-1 {
  width: 630rpx;
  height: 34rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0 0 30rpx;
}
.text_6-1 {
  width: 372rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Light;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_7-1 {
  width: 84rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(64,128,255,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.image-text_2-1 {
  width: 624rpx;
  height: 194rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 28rpx 0 0 30rpx;
}
.image_3-1 {
  width: 194rpx;
  height: 194rpx;
}
.text-group_2-1 {
  width: 406rpx;
  height: 154rpx;
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text_8-1 {
  width: 378rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_9-1 {
  width: 394rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 26rpx;
}
.block_1-1 {
  width: 406rpx;
  height: 34rpx;
  margin-top: 26rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.text_10-1 {
  width: 134rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text-wrapper_3-1 {
  width: 212rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  font-size: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_11-1 {
  width: 212rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_12-1 {
  width: 212rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_13-1 {
  width: 212rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.group_4-1 {
  width: 360rpx;
  height: 62rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 20rpx 0 30rpx 300rpx;
}
.text-wrapper_4-1 {
  border-radius: 200rpx;
  height: 62rpx;
  border: 1px solid rgba(0,0,0,0.3);
  display: flex;
  flex-direction: column;
  width: 162rpx;
}
.text_14-1 {
  width: 112rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 14rpx 0 0 24rpx;
}
.text-wrapper_5-1 {
  background-color: rgba(64,128,255,1.000000);
  border-radius: 200rpx;
  height: 62rpx;
  display: flex;
  flex-direction: column;
  width: 162rpx;
}
.text_15-1 {
  width: 112rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 14rpx 0 0 24rpx;
}
.group_5 {
  width: 750rpx;
  height: 508rpx;
  margin-bottom: 2rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-center;
}
.group_6 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 16rpx;
  width: 690rpx;
  height: 312rpx;
  display: flex;
  flex-direction: column;
  margin: 30rpx 0 0 30rpx;
}
.text-wrapper_6 {
  width: 630rpx;
  height: 34rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0 0 30rpx;
}
.text_16 {
  width: 372rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Light;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_17 {
  width: 84rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(165,165,165,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.image-text_3 {
  width: 624rpx;
  height: 194rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 28rpx 0 26rpx 30rpx;
}
.image_4 {
  width: 194rpx;
  height: 194rpx;
}
.text-group_3 {
  width: 406rpx;
  height: 154rpx;
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text_18 {
  width: 378rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_19 {
  width: 394rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 26rpx;
}
.box_2 {
  width: 406rpx;
  height: 34rpx;
  margin-top: 26rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.text_20 {
  width: 134rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text-wrapper_7 {
  width: 212rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  font-size: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_21 {
  width: 212rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_22 {
  width: 212rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_23 {
  width: 212rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.group_7 {
  background-color: rgba(0,0,0,1.000000);
  border-radius: 8rpx;
  width: 284rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
  margin: 128rpx 0 30rpx 232rpx;
}